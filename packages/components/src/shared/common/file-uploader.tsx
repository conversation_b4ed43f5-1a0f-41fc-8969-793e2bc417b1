import { UploadIcon } from '@mass/icons'
import { useId, useState } from 'react'
import { useTranslation } from 'react-i18next'

interface FileUploaderProps {
  label?: string
  required?: boolean
  multiple?: boolean
  maxSize?: number // in bytes
  onChange?: (files: File[]) => void
  onFileProcess?: (file: File) => Promise<string | number>
  accept?: string
  className?: string
}

export function FileUploader({
  label,
  required = false,
  multiple = false,
  maxSize = 10 * 1024 * 1024, // Default 10MB
  onChange,
  onFileProcess,
  accept = 'application/pdf',
  className,
}: FileUploaderProps): React.ReactElement {
  const id = useId()

  const [files, setFiles] = useState<File[]>([])
  const { t } = useTranslation('common')

  const handleFilesChange = async (selectedFiles: File[]) => {
    const validFiles = selectedFiles.filter(file => accept.includes(file.type) && file.size <= maxSize)

    if (validFiles.length === 0) {
      return
    }

    const updatedFiles = multiple ? [...files, ...validFiles] : validFiles
    setFiles(updatedFiles)

    if (onChange) {
      onChange(updatedFiles)
    }

    if (onFileProcess) {
      const fileIds = await Promise.all(updatedFiles.map(file => onFileProcess(file)))

      // You could do something with fileIds here if needed
    }
  }

  const handleRemoveFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index)
    setFiles(updatedFiles)

    if (onChange) {
      onChange(updatedFiles)
    }
  }

  return (
    <div className={className}>
      {label && (
        <div className='mb-1.5 font-medium text-xs'>
          {label}
          {required && <span className='ml-1 text-red-500'>*</span>}
        </div>
      )}
      <div>
        <div className='rounded-md border border-dashed p-4'>
          <UploadIcon className='mx-auto h-12 w-12 text-muted-foreground' name='untitled:upload-cloud-02' />

          <div className='mt-4 flex justify-center text-center text-muted-foreground text-sm leading-6'>
            <label
              htmlFor={id}
              className='relative cursor-pointer rounded-md font-semibold text-gray-900 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2 hover:text-primary/80'>
              <span>{t('autoFormFields.select_file')}</span>
              <input
                type='file'
                accept={accept}
                multiple={multiple}
                onChange={e => {
                  if (e.target.files) {
                    handleFilesChange(Array.from(e.target.files))
                  }
                }}
                className='hidden'
                id={id}
              />
              <p className='text-muted-foreground text-xs leading-5'>
                {t('autoFormFields.allowed_file_formats', { size: maxSize / 1024 / 1024 })}
              </p>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}
